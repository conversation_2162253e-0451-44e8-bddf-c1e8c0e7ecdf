from __future__ import absolute_import
from __future__ import division
from __future__ import print_function

import _init_paths

import os

import torch
import torch.utils.data
from opts import opts
from models.model import create_model, load_model, save_model
from models.data_parallel import DataParallel
from logger import Logger
# 修改这一行，使用绝对导入
from lib.datasets.dataset_factory import get_dataset
from trains.train_factory import train_factory
from models.classifier import Processor


def get_tableme_dataset(opt):
  """
  获取TableLabelMe数据集类，集成配置系统和数据集加载器

  Args:
      opt: 命令行参数对象，包含TableLabelMe配置信息

  Returns:
      class: TableLabelMe数据集类
  """
  print("[信息] 初始化TableLabelMe数据集...")

  # 导入TableLabelMe数据集类
  from lib.datasets.dataset.table_labelmev2 import Table as TableLabelMeDataset

  # 验证TableLabelMe配置
  if not hasattr(opt, 'dataset_mode') or opt.dataset_mode != 'TableLabelMe':
    raise ValueError("TableLabelMe模式需要正确的dataset_mode配置")

  if not hasattr(opt, 'config_data') or opt.config_data is None:
    print("[警告] 未检测到TableLabelMe配置数据，将使用默认配置")

  # 验证数据路径配置
  data_paths = getattr(opt, 'data_paths', {})
  if data_paths:
    train_paths = data_paths.get('train', [])
    val_paths = data_paths.get('val', [])
    print(f"[信息] TableLabelMe数据路径 - 训练: {len(train_paths)}个, 验证: {len(val_paths)}个")
  else:
    print("[信息] 使用TableLabelMe默认数据路径")

  # 返回配置好的TableLabelMe数据集类
  print("[信息] TableLabelMe数据集初始化完成")
  return TableLabelMeDataset


def main(opt):
  torch.autograd.set_detect_anomaly(True)
  torch.manual_seed(opt.seed)
  torch.backends.cudnn.benchmark = not opt.not_cuda_benchmark and not opt.test

  # TableLabelMe支持：数据集模式检测和配置集成（迭代4新增）
  dataset_mode = getattr(opt, 'dataset_mode', 'COCO')
  print(f"[信息] 检测到数据集模式: {dataset_mode}")

  # 根据模式选择合适的数据集创建策略
  if dataset_mode == 'TableLabelMe':
    Dataset = get_tableme_dataset(opt)
    print(f"[信息] 使用TableLabelMe数据集类: {Dataset.__name__}")
  else:
    Dataset = get_dataset(opt.dataset, opt.task)
    print(f"[信息] 使用COCO兼容数据集: {opt.dataset}")

  # 更新数据集信息和设置头部
  opt = opts().update_dataset_info_and_set_heads(opt, Dataset)

  # 输出数据集配置信息
  print(f"[信息] 数据集配置完成 - 类别数: {opt.num_classes}, 分辨率: {opt.input_h}x{opt.input_w}")
  if dataset_mode == 'TableLabelMe':
    config_data = getattr(opt, 'config_data', None)
    if config_data:
      print(f"[信息] TableLabelMe配置: {config_data.get('description', '无描述')}")
    unified_config = getattr(opt, 'unified_config', None)
    if unified_config:
      metadata = unified_config.get('config_metadata', {})
      print(f"[信息] 配置状态: {metadata.get('validation_status', '未知')}")

  print(f"[信息] 数据集模式 {dataset_mode} 初始化完成")
  #print(opt)

  logger = Logger(opt)

  os.environ['CUDA_VISIBLE_DEVICES'] = opt.gpus_str
  opt.device = torch.device('cuda' if opt.gpus[0] >= 0 else 'cpu')
  
  model = create_model(opt.arch, opt.heads, opt.head_conv)
  Trainer = train_factory[opt.task]
 

  processor = Processor(opt)
  processor.train()
  optimizer = torch.optim.Adam([  \
              {'params': model.parameters()}, \
              {'params': processor.parameters()}],  lr =opt.lr, betas= (0.9, 0.98), eps=1e-9)

  trainer = Trainer(opt, model, optimizer, processor)



  start_epoch = 0
  if opt.load_model != '':
    #model, optimizer, start_epoch = load_model(model, opt.load_model)
      #model, opt.load_model, optimizer, opt.resume, opt.lr, opt.lr_step)
    model = load_model(model, opt.load_model)

  if opt.load_processor != '':
    processor = load_model(processor, opt.load_processor)
  
  trainer.set_device(opt.gpus, opt.chunk_sizes, opt.device)

  val_loader = torch.utils.data.DataLoader(
      Dataset(opt, 'val'), 
      batch_size=1, 
      shuffle=False,
      num_workers=1,
      pin_memory=True
  )

  if opt.test:
    _, preds = trainer.val(0, val_loader)
    val_loader.dataset.run_eval(preds, opt.save_dir)
    return

  train_loader = torch.utils.data.DataLoader(
      Dataset(opt, 'train'), 
      batch_size=opt.batch_size, 
      shuffle=True,
      num_workers=opt.num_workers,
      pin_memory=True,
      drop_last=True
  )

  # TableLabelMe支持：训练流程集成验证（迭代4新增）
  print(f"[信息] 训练流程集成验证...")
  print(f"[信息] 数据集模式: {dataset_mode}")
  print(f"[信息] 训练样本数: {len(train_loader.dataset)}")
  print(f"[信息] 验证样本数: {len(val_loader.dataset)}")
  print(f"[信息] 批次大小: {opt.batch_size}")
  print(f"[信息] 训练轮数: {opt.num_epochs}")

  # 验证数据集兼容性
  try:
    # 测试数据加载
    sample_batch = next(iter(train_loader))
    print(f"[信息] 数据加载测试成功 - 批次形状: {sample_batch['input'].shape if 'input' in sample_batch else '未知'}")
  except Exception as e:
    print(f"[警告] 数据加载测试失败: {e}")

  print(f"[信息] {dataset_mode}模式训练流程集成完成")
  print('Starting training...')
  best = 1e10

  for epoch in range(start_epoch + 1, opt.num_epochs + 1):
    mark = epoch if opt.save_all else 'last'
    log_dict_train, _ = trainer.train(epoch, train_loader)

    logger.write('epoch: {} |'.format(epoch))
    for k, v in log_dict_train.items():
      logger.scalar_summary('train_{}'.format(k), v, epoch)
      logger.write('{} {:8f} | '.format(k, v))
   
    if opt.val_intervals > 0 and epoch % opt.val_intervals == 0:
      save_model(os.path.join(opt.save_dir, 'processor_{}.pth'.format(mark)), 
                epoch, processor, optimizer)
      save_model(os.path.join(opt.save_dir, 'model_{}.pth'.format(mark)), 
                epoch, model, optimizer)
      with torch.no_grad():
        log_dict_val, preds = trainer.val(epoch, val_loader)
      for k, v in log_dict_val.items():
        logger.scalar_summary('val_{}'.format(k), v, epoch)
        logger.write('{} {:8f} | '.format(k, v))

      if log_dict_val[opt.metric] < best:
        best = log_dict_val[opt.metric]
        save_model(os.path.join(opt.save_dir, 'processor_best.pth'), 
                  epoch, processor)
        save_model(os.path.join(opt.save_dir, 'model_best.pth'), 
                  epoch, model)
    else:
      save_model(os.path.join(opt.save_dir, 'processor_last.pth'), 
                epoch, processor, optimizer)
      save_model(os.path.join(opt.save_dir, 'model_last.pth'), 
                epoch, model, optimizer)
    logger.write('\n')
    if epoch in opt.lr_step:
      save_model(os.path.join(opt.save_dir, 'processor_{}.pth'.format(epoch)), 
                epoch, processor, optimizer)
      save_model(os.path.join(opt.save_dir, 'model_{}.pth'.format(epoch)), 
                epoch, model, optimizer)
      lr = opt.lr * (0.1 ** (opt.lr_step.index(epoch) + 1))
      print('Drop LR to', lr)
      for param_group in optimizer.param_groups:
          param_group['lr'] = lr
    
  logger.close()

if __name__ == '__main__':
  opt = opts().parse()
  main(opt)
