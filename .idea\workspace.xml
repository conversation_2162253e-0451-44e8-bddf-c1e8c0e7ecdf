<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="cd9e17ef-ec20-412b-ab37-d6865fd85370" name="Changes" comment="init project">
      <change afterPath="$PROJECT_DIR$/.gitignore" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/.gitignore" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/AugmentWebviewStateStore.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/LORE-TSR.iml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/deployment.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/inspectionProfiles/profiles_settings.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/material_theme_project_new.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/misc.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/modules.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/vcs.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/LICENSE.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/NOTICE" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/README.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/README_fix_DCN_installation.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/README_fix_DCN_installation_old.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/cmd.sh" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/debug_case_sensitivity.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/input_images/wired/wired_sample1.jpg" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/input_images/wired/wired_sample2.jpg" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/input_images/wireless/wireless_sample1.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/input_images/wireless/wireless_sample2.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/requirements.txt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/run_as_module.sh" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/_init_paths.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/ckpt_wired/ckpt_wtw.zip" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/ckpt_wired/ckpt_wtw/model_best.pth" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/ckpt_wired/ckpt_wtw/processor_best.pth" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/ckpt_wireless/ckpt_wireless.zip" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/ckpt_wireless/model_best.pth" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/ckpt_wireless/processor_best.pth" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/demo.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/eval.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/eval.sh" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/configs/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/configs/config_examples.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/configs/dataset_configs.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/datasets/dataset/table.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/datasets/dataset/table_labelmev2.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/datasets/dataset/table_mid.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/datasets/dataset/table_small.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/datasets/dataset_factory.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/datasets/parsers/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/datasets/parsers/base_parser.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/datasets/parsers/file_scanner.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/datasets/parsers/quality_filter.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/datasets/parsers/tablelabelme_parser.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/datasets/sample/ctdet.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/detectors/base_detector.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/detectors/ctdet.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/detectors/detector_factory.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/external/Makefile" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/external/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/external/nms.pyx" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/external/setup.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/external/shapelyNMS.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/logger.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/classifier.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/data_parallel.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/decode.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/losses.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/model.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/DCNv2/LICENSE" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/DCNv2/README.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/DCNv2/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/DCNv2/dcn_v2.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/DCNv2/dcn_v2_alt.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/DCNv2/dcn_v2_onnx.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/DCNv2/direct_build.sh" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/DCNv2/install.sh" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/DCNv2/install_cuda_fix.sh" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/DCNv2/install_once.sh" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/DCNv2/make.sh" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/DCNv2/set_env.sh" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/DCNv2/setup.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/DCNv2/src/cpu/dcn_v2_cpu.cpp" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/DCNv2/src/cpu/dcn_v2_im2col_cpu.cpp" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/DCNv2/src/cpu/dcn_v2_im2col_cpu.h" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/DCNv2/src/cpu/dcn_v2_psroi_pooling_cpu.cpp" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/DCNv2/src/cpu/vision.h" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/DCNv2/src/cuda/dcn_v2_cuda.cu" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/DCNv2/src/cuda/dcn_v2_im2col_cuda.cu" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/DCNv2/src/cuda/dcn_v2_im2col_cuda.h" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/DCNv2/src/cuda/dcn_v2_psroi_pooling_cuda.cu" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/DCNv2/src/cuda/vision.h" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/DCNv2/src/dcn_v2.h" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/DCNv2/src/vision.cpp" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/DCNv2/testcpu.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/DCNv2/testcuda.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/DCNv2_1.4/LICENSE" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/DCNv2_1.4/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/DCNv2_1.4/dcn_v2.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/DCNv2_1.4/make.sh" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/DCNv2_1.4/setup.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/DCNv2_1.4/src/cpu/dcn_v2_cpu.cpp" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/DCNv2_1.4/src/cpu/dcn_v2_im2col_cpu.cpp" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/DCNv2_1.4/src/cpu/dcn_v2_im2col_cpu.h" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/DCNv2_1.4/src/cpu/dcn_v2_psroi_pooling_cpu.cpp" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/DCNv2_1.4/src/cpu/vision.h" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/DCNv2_1.4/src/cuda/dcn_v2_cuda.cu" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/DCNv2_1.4/src/cuda/dcn_v2_im2col_cuda.cu" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/DCNv2_1.4/src/cuda/dcn_v2_im2col_cuda.h" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/DCNv2_1.4/src/cuda/dcn_v2_psroi_pooling_cuda.cu" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/DCNv2_1.4/src/cuda/vision.h" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/DCNv2_1.4/src/dcn_v2.h" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/DCNv2_1.4/src/vision.cpp" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/DCNv2_1.4/testcpu.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/DCNv2_1.4/testcuda.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/DCNv2_original/LICENSE" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/DCNv2_original/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/DCNv2_original/dcn_v2.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/DCNv2_original/make.sh" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/DCNv2_original/setup.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/DCNv2_original/src/cpu/dcn_v2_cpu.cpp" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/DCNv2_original/src/cpu/dcn_v2_im2col_cpu.cpp" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/DCNv2_original/src/cpu/dcn_v2_im2col_cpu.h" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/DCNv2_original/src/cpu/dcn_v2_psroi_pooling_cpu.cpp" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/DCNv2_original/src/cpu/vision.h" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/DCNv2_original/src/cuda/dcn_v2_cuda.cu" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/DCNv2_original/src/cuda/dcn_v2_im2col_cuda.cu" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/DCNv2_original/src/cuda/dcn_v2_im2col_cuda.h" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/DCNv2_original/src/cuda/dcn_v2_psroi_pooling_cuda.cu" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/DCNv2_original/src/cuda/vision.h" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/DCNv2_original/src/dcn_v2.h" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/DCNv2_original/src/vision.cpp" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/DCNv2_original/testcpu.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/DCNv2_original/testcuda.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/dcn/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/dcn/build/lib.linux-x86_64-3.6/deform_conv_cuda.cpython-36m-x86_64-linux-gnu.so" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/dcn/build/lib.linux-x86_64-3.6/deform_pool_cuda.cpython-36m-x86_64-linux-gnu.so" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/dcn/build/temp.linux-x86_64-3.6/src/deform_conv_cuda.o" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/dcn/build/temp.linux-x86_64-3.6/src/deform_conv_cuda_kernel.o" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/dcn/build/temp.linux-x86_64-3.6/src/deform_pool_cuda.o" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/dcn/build/temp.linux-x86_64-3.6/src/deform_pool_cuda_kernel.o" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/dcn/deform_conv_cuda.cpython-36m-x86_64-linux-gnu.so" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/dcn/deform_pool_cuda.cpython-36m-x86_64-linux-gnu.so" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/dcn/functions/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/dcn/functions/deform_conv.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/dcn/functions/deform_pool.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/dcn/modules/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/dcn/modules/deform_conv.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/dcn/modules/deform_pool.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/dcn/setup.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/dcn/src/deform_conv_cuda.cpp" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/dcn/src/deform_conv_cuda_kernel.cu" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/dcn/src/deform_pool_cuda.cpp" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/dcn/src/deform_pool_cuda_kernel.cu" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/dlav0.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/fpn_mask_resnet.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/fpn_mask_resnet_half.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/fpn_resnet.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/fpn_resnet_half.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/pose_dla_dcn.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/networks/resnet_dcn.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/scatter_gather.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/transformer.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/models/utils.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/opts.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/trains/base_trainer.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/trains/ctdet.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/trains/train_factory.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/utils/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/utils/adjacency.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/utils/config_loader.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/utils/debugger.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/utils/eval_utils.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/utils/image.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/utils/logger_config.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/utils/oracle_utils.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/utils/post_process.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/lib/utils/utils.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/run_training.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/scripts/infer/demo_test.sh" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/scripts/infer/demo_wired.sh" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/scripts/infer/demo_wireless.sh" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/scripts/infer/demo_wireless_mine.sh" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/scripts/train/train_wired.sh" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/scripts/train/train_wired_single.sh" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/scripts/train/train_wireless.sh" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/scripts/train/train_wireless_arcres.sh" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/vibecoding-adapt_modern_datasets_to_LORE/reports/step_4_5_report.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/vibecoding-adapt_modern_datasets_to_LORE/docs/0-readme_LORE_callchain.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/vibecoding-adapt_modern_datasets_to_LORE/docs/0-readme_LORE_dataflow.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/vibecoding-adapt_modern_datasets_to_LORE/docs/1-readme_adaption_prd.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/vibecoding-adapt_modern_datasets_to_LORE/docs/2-readme_adaption_prdplan.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/vibecoding-adapt_modern_datasets_to_LORE/docs/2-readme_adaption_prdplan_bak.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/vibecoding-adapt_modern_datasets_to_LORE/docs/3-readme_adaption_lld_iter1.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/vibecoding-adapt_modern_datasets_to_LORE/docs/3-readme_adaption_lld_iter2.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/vibecoding-adapt_modern_datasets_to_LORE/docs/3-readme_adaption_lld_iter3.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/vibecoding-adapt_modern_datasets_to_LORE/docs/3-readme_adaption_lld_iter4.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/vibecoding-adapt_modern_datasets_to_LORE/docs/3-readme_adaption_lld_iter5.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/vibecoding-adapt_modern_datasets_to_LORE/docs/4-readme_codingplan_codingplan_iter1.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/vibecoding-adapt_modern_datasets_to_LORE/docs/4-readme_codingplan_codingplan_iter2.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/vibecoding-adapt_modern_datasets_to_LORE/docs/4-readme_codingplan_codingplan_iter3.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/vibecoding-adapt_modern_datasets_to_LORE/docs/4-readme_codingplan_codingplan_iter4.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/vibecoding-adapt_modern_datasets_to_LORE/reports/step_1_1_report.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/vibecoding-adapt_modern_datasets_to_LORE/reports/step_1_2_report.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/vibecoding-adapt_modern_datasets_to_LORE/reports/step_1_3_report.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/vibecoding-adapt_modern_datasets_to_LORE/reports/step_1_4_report.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/vibecoding-adapt_modern_datasets_to_LORE/reports/step_1_5_report.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/vibecoding-adapt_modern_datasets_to_LORE/reports/step_1_6_report.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/vibecoding-adapt_modern_datasets_to_LORE/reports/step_2_1_report.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/vibecoding-adapt_modern_datasets_to_LORE/reports/step_2_2_report.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/vibecoding-adapt_modern_datasets_to_LORE/reports/step_2_3_report.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/vibecoding-adapt_modern_datasets_to_LORE/reports/step_2_4_report.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/vibecoding-adapt_modern_datasets_to_LORE/reports/step_2_5_report.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/vibecoding-adapt_modern_datasets_to_LORE/reports/step_3_1_report.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/vibecoding-adapt_modern_datasets_to_LORE/reports/step_3_2_report.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/vibecoding-adapt_modern_datasets_to_LORE/reports/step_3_3_report.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/vibecoding-adapt_modern_datasets_to_LORE/reports/step_3_4_report.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/vibecoding-adapt_modern_datasets_to_LORE/reports/step_3_5_report.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/vibecoding-adapt_modern_datasets_to_LORE/reports/step_4_1_report.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/vibecoding-adapt_modern_datasets_to_LORE/reports/step_4_2_report.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/vibecoding-adapt_modern_datasets_to_LORE/reports/step_4_3_report.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/vibecoding-adapt_modern_datasets_to_LORE/reports/step_4_4_report.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/vibecoding-adapt_modern_datasets_to_LORE/rules/0-parsecallchain.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/vibecoding-adapt_modern_datasets_to_LORE/rules/0-parsecallchain.prompt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/vibecoding-adapt_modern_datasets_to_LORE/rules/1-prd.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/vibecoding-adapt_modern_datasets_to_LORE/rules/10-save-timeline.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/vibecoding-adapt_modern_datasets_to_LORE/rules/11-gen-msg.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/vibecoding-adapt_modern_datasets_to_LORE/rules/11-gen-msg.prompt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/vibecoding-adapt_modern_datasets_to_LORE/rules/12-human-read-code.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/vibecoding-adapt_modern_datasets_to_LORE/rules/13-auto-gen-msg.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/vibecoding-adapt_modern_datasets_to_LORE/rules/2-prdplan.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/vibecoding-adapt_modern_datasets_to_LORE/rules/5-lld.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/vibecoding-adapt_modern_datasets_to_LORE/rules/6-codingplan.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/vibecoding-adapt_modern_datasets_to_LORE/rules/6-codingplanv2.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/vibecoding-adapt_modern_datasets_to_LORE/rules/6-codingplanv3.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/vibecoding-adapt_modern_datasets_to_LORE/rules/6-codingplanv4.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/vibecoding-adapt_modern_datasets_to_LORE/rules/6-codingplanv5.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/vibecoding-adapt_modern_datasets_to_LORE/rules/7-codingstep.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/vibecoding-adapt_modern_datasets_to_LORE/rules/7-codingstepv2.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/vibecoding-adapt_modern_datasets_to_LORE/rules/7-codingstepv3.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/vibecoding-adapt_modern_datasets_to_LORE/rules/code-master.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/vibecoding-adapt_modern_datasets_to_LORE/rules/readpaper.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/vibecoding-adapt_modern_datasets_to_LORE/rules/readpaper_lgf.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/vibecoding-adapt_modern_datasets_to_LORE/rules/self-prompter.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/vibecoding-adapt_modern_datasets_to_LORE/vibe_utils/INDEX.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/vibecoding-adapt_modern_datasets_to_LORE/vibe_utils/README.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/vibecoding-adapt_modern_datasets_to_LORE/vibe_utils/WTW-coco-test.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/vibecoding-adapt_modern_datasets_to_LORE/vibe_utils/WTW_COCO_Fields_Analysis_Report.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/vibecoding-adapt_modern_datasets_to_LORE/vibe_utils/analysis_output/analysis_report.txt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/vibecoding-adapt_modern_datasets_to_LORE/vibe_utils/analysis_output/coco_format_samples.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/vibecoding-adapt_modern_datasets_to_LORE/vibe_utils/analysis_output/full_stats.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/vibecoding-adapt_modern_datasets_to_LORE/vibe_utils/analysis_output/sample_data.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/vibecoding-adapt_modern_datasets_to_LORE/vibe_utils/analyze_coco_dataset.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/vibecoding-adapt_modern_datasets_to_LORE/vibe_utils/extract_coco_samples.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/vibecoding-adapt_modern_datasets_to_LORE/vibe_utils/wtw_coco_samples.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/vibecoding-adapt_modern_datasets_to_LORE/vibe_utils/wtw_tableme_sample.json" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$/cocoapi" value="master" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 0
}</component>
  <component name="ProjectId" id="2ziLYuOl8cQREWN9X32RFXr1sZl" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "Python.train_single.executor": "Run",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "WebServerToolWindowPanel.toolwindow.highlight.mappings": "true",
    "WebServerToolWindowPanel.toolwindow.highlight.symlinks": "true",
    "WebServerToolWindowPanel.toolwindow.show.date": "false",
    "WebServerToolWindowPanel.toolwindow.show.permissions": "false",
    "WebServerToolWindowPanel.toolwindow.show.size": "false",
    "git-widget-placeholder": "master",
    "last_opened_file_path": "D:/workspace/projects/LORE-TSR-adapt",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "settings.editor.selected.configurable": "preferences.pluginManager",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\workspace\projects\TSRTransplantation\LORE-TSR\vibecoding-adapt_modern_datasets_to_LORE" />
      <recent name="D:\workspace\projects\TSRTransplantation\LORE-TSR" />
      <recent name="D:\workspace\projects\TSRTransplantation\LORE-TSR\src\scripts\train" />
      <recent name="D:\workspace\projects\TSRTransplantation\LORE-TSR\src\scripts\infer" />
      <recent name="D:\workspace\projects\TSRTransplantation\LORE-TSR\src\bak" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\workspace\projects\TSRTransplantation\LORE-TSR\src\lib\models\networks\bak\DCNv_latest" />
      <recent name="D:\workspace\projects\TSRTransplantation\LORE-TSR\src\lib\models\networks" />
    </key>
  </component>
  <component name="RunManager">
    <configuration name="train_single" type="PythonConfigurationType" factoryName="Python">
      <module name="LORE-TSR" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="SDK_NAME" value="Remote Python 3.10.13 (sftp://root@************:1322/opt/miniforge3/envs/lore/bin/python3)" />
      <option name="WORKING_DIRECTORY" value="D:\workspace\projects\TSRTransplantation\LORE-TSR\src" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/src/main.py" />
      <option name="PARAMETERS" value="ctdet --dataset_name table --exp_id training_wtw --dataset_name WTW --image_dir /aipdf-mlp/shared/tsr_dataset/WTW/train --wiz_4ps --wiz_stacking --wiz_pairloss --tsfm_layers 3 --stacking_layers 3 --batch_size 2 --master_batch 6 --arch dla_34 --lr 1e-4 --K 500 --MK 1000 --num_epochs 100 --lr_step '70, 90' --gpus 0 --num_workers 16 --val_intervals 10" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-f27c65a3e318-JavaScript-PY-251.23774.444" />
        <option value="bundled-python-sdk-890ed5b35930-d9c5bdb153f4-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-251.23774.444" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="cd9e17ef-ec20-412b-ab37-d6865fd85370" name="Changes" comment="" />
      <created>1752208998120</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1752208998120</updated>
      <workItem from="1752208999237" duration="60000" />
      <workItem from="1752209088998" duration="1159000" />
      <workItem from="1752210286129" duration="17755000" />
      <workItem from="1752455455994" duration="6224000" />
      <workItem from="1752462214569" duration="8496000" />
      <workItem from="1752550697505" duration="12645000" />
      <workItem from="1752589119682" duration="178000" />
      <workItem from="1752592015609" duration="15116000" />
      <workItem from="1752809820235" duration="621000" />
      <workItem from="1752945273221" duration="1505000" />
      <workItem from="1752971451534" duration="2849000" />
      <workItem from="1752982398241" duration="1655000" />
      <workItem from="1753022997361" duration="678000" />
      <workItem from="1753026402236" duration="18926000" />
      <workItem from="1753162632258" duration="601000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="init project" />
    <option name="LAST_COMMIT_MESSAGE" value="init project" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/src/lib/opts.py</url>
          <line>81</line>
          <option name="timeStamp" value="1" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/src/lib/opts.py</url>
          <line>358</line>
          <option name="timeStamp" value="2" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/src/lib/datasets/dataset/table.py</url>
          <line>26</line>
          <option name="timeStamp" value="5" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/src/demo.py</url>
          <line>26</line>
          <option name="timeStamp" value="6" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/LORE_TSR$train_single.coverage" NAME="train_single Coverage Results" MODIFIED="1752234376515" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="D:\workspace\projects\TSRTransplantation\LORE-TSR\src" />
  </component>
</project>