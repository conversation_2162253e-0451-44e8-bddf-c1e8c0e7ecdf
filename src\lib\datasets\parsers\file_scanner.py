"""
专门负责TableLabelMe数据集的目录扫描、文件匹配和索引构建的核心模块。

该模块实现递归目录遍历、图像-标注文件对匹配、唯一ID生成和统计信息收集，
为TableLabelMe格式数据的加载提供完整的文件索引构建功能。
"""

from __future__ import absolute_import
from __future__ import division
from __future__ import print_function

import os
import re
import hashlib
import time
from typing import Dict, Any, List, Optional, Union, Tuple


class FileScanner:
    """
    TableLabelMe数据集的目录扫描和文件索引构建器。
    
    该类负责扫描TableLabelMe格式数据集的目录结构，识别part_xxxx子目录，
    匹配图像-标注文件对，生成唯一ID，并构建完整的文件索引。
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化FileScanner，设置扫描配置参数。
        
        Args:
            config (Optional[Dict[str, Any]]): 扫描配置字典，包含各种扫描参数
                如果为None，将使用默认配置
        """
        # 设置默认配置
        self.default_config = {
            "part_pattern": r"part_\d{4}",                    # part目录匹配模式
            "image_extensions": [".jpg", ".jpeg", ".png"],    # 支持的图像格式
            "annotation_patterns": [".json", "_table_annotation.json"],  # 标注文件模式
            "max_depth": 3,                                   # 最大扫描深度
            "follow_symlinks": False,                         # 是否跟随符号链接
            "case_sensitive": False                           # 文件名匹配是否区分大小写
        }
        
        # 合并用户配置和默认配置
        if config is None:
            self.config = self.default_config.copy()
        else:
            self.config = self.default_config.copy()
            self.config.update(config)
        
        # 编译正则表达式模式
        self.part_pattern = re.compile(self.config["part_pattern"])
        
        # 统计信息
        self.scan_statistics = {}
    
    def scan_directories(self, data_paths: List[str], split: str) -> Dict[str, Any]:
        """
        扫描多个数据源目录，构建完整的文件索引。

        Args:
            data_paths (List[str]): 数据路径列表，如['/path/to/dataset1', '/path/to/dataset2']
            split (str): 数据集分割类型，如'train'或'val'

        Returns:
            Dict[str, Any]: 包含文件索引和统计信息的字典
                {
                    'file_index': Dict[int, Dict[str, str]],  # 文件索引字典
                    'statistics': Dict[str, Any]              # 扫描统计信息
                }
        """
        # 记录开始时间
        start_time = time.time()

        # 初始化结果字典
        combined_file_index = {}
        all_statistics = []

        # 遍历每个数据路径
        for data_path in data_paths:
            if not os.path.exists(data_path):
                continue

            # 扫描单个目录
            dataset_source = os.path.basename(data_path)
            single_result = self._scan_single_directory(data_path, dataset_source)

            # 合并结果
            if single_result:
                combined_file_index.update(single_result.get('file_index', {}))
                all_statistics.append(single_result.get('statistics', {}))

        # 计算扫描时间
        scan_time = time.time() - start_time

        # 收集统计信息
        combined_statistics = self._collect_statistics(all_statistics)
        combined_statistics['scan_time'] = scan_time

        return {
            'file_index': combined_file_index,
            'statistics': combined_statistics
        }
    
    def _scan_single_directory(self, root_path: str, dataset_source: str) -> Optional[Dict[str, Any]]:
        """
        扫描单个数据源目录，识别part子目录并构建文件映射。

        Args:
            root_path (str): 数据源根目录路径
            dataset_source (str): 数据源标识名称

        Returns:
            Optional[Dict[str, Any]]: 该数据源的文件索引字典和统计信息
                如果扫描失败返回None
        """
        if not os.path.isdir(root_path):
            return None

        # 查找part目录
        part_directories = self._find_part_directories(root_path)
        if not part_directories:
            return None

        # 初始化结果
        file_index = {}
        part_statistics = []

        # 扫描每个part目录
        for part_dir in part_directories:
            part_result = self._scan_part_directory(part_dir, dataset_source)

            # 合并文件索引
            file_index.update(part_result['file_index'])

            # 收集part统计信息
            part_statistics.append(part_result['statistics'])

        # 汇总统计信息
        total_images = sum(stat.get('total_images', 0) for stat in part_statistics)
        total_annotations = sum(stat.get('total_annotations', 0) for stat in part_statistics)
        valid_pairs = sum(stat.get('valid_pairs', 0) for stat in part_statistics)
        orphan_images = sum(stat.get('orphan_images', 0) for stat in part_statistics)
        orphan_annotations = sum(stat.get('orphan_annotations', 0) for stat in part_statistics)

        # 构建统计信息
        statistics = {
            'dataset_source': dataset_source,
            'root_path': root_path,
            'part_directories': len(part_directories),
            'total_images': total_images,
            'total_annotations': total_annotations,
            'valid_pairs': valid_pairs,
            'orphan_images': orphan_images,
            'orphan_annotations': orphan_annotations,
            'part_details': part_statistics
        }

        return {
            'file_index': file_index,
            'statistics': statistics
        }
    
    def _find_part_directories(self, root_path: str) -> List[str]:
        """
        在根目录下查找符合part_xxxx模式的子目录。
        
        Args:
            root_path (str): 根目录路径
            
        Returns:
            List[str]: part目录路径列表
        """
        part_directories = []
        
        try:
            # 遍历根目录下的所有项目
            for item in os.listdir(root_path):
                item_path = os.path.join(root_path, item)
                
                # 检查是否为目录
                if not os.path.isdir(item_path):
                    continue
                
                # 检查是否符合part模式
                if self.part_pattern.match(item):
                    part_directories.append(item_path)
        
        except (OSError, PermissionError):
            # 遵循fail-fast原则，目录访问错误时返回空列表
            return []
        
        # 按目录名排序，确保处理顺序的一致性
        part_directories.sort()
        return part_directories
    
    def _normalize_path(self, path: str) -> str:
        """
        路径规范化，确保跨平台兼容性。
        
        Args:
            path (str): 原始路径字符串
            
        Returns:
            str: 规范化后的路径字符串
        """
        # 转换为绝对路径
        abs_path = os.path.abspath(path)
        
        # 规范化路径分隔符
        normalized_path = os.path.normpath(abs_path)
        
        return normalized_path
    
    def _collect_statistics(self, statistics_list: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        收集和汇总扫描统计信息。

        Args:
            statistics_list (List[Dict[str, Any]]): 各个数据源的统计信息列表

        Returns:
            Dict[str, Any]: 汇总的统计信息
        """
        if not statistics_list:
            return {
                'total_directories': 0,
                'part_directories': 0,
                'total_images': 0,
                'total_annotations': 0,
                'valid_pairs': 0,
                'orphan_images': 0,
                'orphan_annotations': 0,
                'duplicate_ids': 0,
                'scan_time': 0.0,
                'data_sources': []
            }

        # 汇总统计信息
        total_part_directories = sum(stat.get('part_directories', 0) for stat in statistics_list)
        total_images = sum(stat.get('total_images', 0) for stat in statistics_list)
        total_annotations = sum(stat.get('total_annotations', 0) for stat in statistics_list)
        total_valid_pairs = sum(stat.get('valid_pairs', 0) for stat in statistics_list)
        total_orphan_images = sum(stat.get('orphan_images', 0) for stat in statistics_list)
        total_orphan_annotations = sum(stat.get('orphan_annotations', 0) for stat in statistics_list)

        # 计算ID冲突数量（这里简化处理，实际应该检查重复ID）
        duplicate_ids = 0  # 在实际使用中可以通过检查file_index的key重复来计算

        # 计算扫描时间（如果有的话）
        scan_time = sum(stat.get('scan_time', 0.0) for stat in statistics_list)

        return {
            'total_directories': len(statistics_list),
            'part_directories': total_part_directories,
            'total_images': total_images,
            'total_annotations': total_annotations,
            'valid_pairs': total_valid_pairs,
            'orphan_images': total_orphan_images,
            'orphan_annotations': total_orphan_annotations,
            'duplicate_ids': duplicate_ids,
            'scan_time': scan_time,
            'data_sources': [stat.get('dataset_source', 'unknown') for stat in statistics_list],
            'success_rate': (total_valid_pairs / max(total_images, 1)) * 100.0  # 成功匹配率
        }

    def _generate_image_id(self, file_path: str) -> int:
        """
        基于文件路径生成稳定唯一的图像ID。

        Args:
            file_path (str): 图像文件路径

        Returns:
            int: 64位整数ID

        Note:
            使用MD5哈希算法确保ID的稳定性和唯一性，
            相同路径总是生成相同的ID。与BaseParser保持一致。
        """
        # 规范化路径以确保跨平台一致性
        normalized_path = self._normalize_path(file_path)

        # 使用MD5哈希生成稳定的ID
        hash_object = hashlib.md5(normalized_path.encode('utf-8'))
        # 取前16位十六进制字符转换为64位整数
        return int(hash_object.hexdigest()[:16], 16)

    def _validate_file_pair(self, image_path: str, annotation_path: str) -> bool:
        """
        验证图像-标注文件对的有效性。

        Args:
            image_path (str): 图像文件路径
            annotation_path (str): 标注文件路径

        Returns:
            bool: 验证结果，True表示文件对有效

        Note:
            检查文件存在性和可读性，遵循fail-fast原则。
        """
        try:
            # 检查图像文件
            if not os.path.isfile(image_path):
                return False
            if not os.access(image_path, os.R_OK):
                return False

            # 检查标注文件
            if not os.path.isfile(annotation_path):
                return False
            if not os.access(annotation_path, os.R_OK):
                return False

            return True

        except (OSError, PermissionError):
            # 遵循fail-fast原则，文件访问错误时返回False
            return False

    def _match_image_annotation_pairs(self, files: List[str]) -> Dict[str, Any]:
        """
        在part目录中匹配图像文件和对应的标注文件。

        Args:
            files (List[str]): 目录中的文件列表

        Returns:
            Dict[str, Any]: 匹配结果字典，包含匹配对列表和统计信息
                {
                    'matched_pairs': List[Tuple[str, str]],  # (image_path, annotation_path)
                    'orphan_images': List[str],              # 孤儿图像文件
                    'orphan_annotations': List[str],         # 孤儿标注文件
                    'total_images': int,                     # 图像文件总数
                    'total_annotations': int                 # 标注文件总数
                }
        """
        # 分离图像和标注文件
        image_files = []
        annotation_files = []

        # 获取配置
        image_extensions = self.config.get('image_extensions', ['.jpg', '.jpeg', '.png'])
        annotation_patterns = self.config.get('annotation_patterns', ['.json', '_table_annotation.json'])
        case_sensitive = self.config.get('case_sensitive', False)

        # 分类文件
        for file_path in files:
            file_name = os.path.basename(file_path)

            # 处理大小写敏感性
            if not case_sensitive:
                file_name_lower = file_name.lower()
                # 检查是否为图像文件
                if any(file_name_lower.endswith(ext.lower()) for ext in image_extensions):
                    image_files.append(file_path)
                # 检查是否为标注文件
                elif any(file_name_lower.endswith(pattern.lower()) for pattern in annotation_patterns):
                    annotation_files.append(file_path)
            else:
                # 检查是否为图像文件
                if any(file_name.endswith(ext) for ext in image_extensions):
                    image_files.append(file_path)
                # 检查是否为标注文件
                elif any(file_name.endswith(pattern) for pattern in annotation_patterns):
                    annotation_files.append(file_path)

        # 按文件名前缀分组进行匹配
        matched_pairs = []
        orphan_images = []
        orphan_annotations = annotation_files.copy()  # 初始时所有标注文件都是孤儿

        for image_path in image_files:
            image_name = os.path.basename(image_path)
            # 获取文件名前缀（去除扩展名）
            image_prefix = os.path.splitext(image_name)[0]

            # 查找对应的标注文件
            matched_annotation = None

            # 优先匹配 .json 文件
            for annotation_path in annotation_files:
                annotation_name = os.path.basename(annotation_path)

                # 检查 image.jpg -> image.json 模式
                if annotation_name == image_prefix + '.json':
                    matched_annotation = annotation_path
                    break
                # 检查 image.jpg -> image_table_annotation.json 模式
                elif annotation_name == image_prefix + '_table_annotation.json':
                    matched_annotation = annotation_path
                    break

            if matched_annotation:
                matched_pairs.append((image_path, matched_annotation))
                # 从孤儿标注列表中移除
                if matched_annotation in orphan_annotations:
                    orphan_annotations.remove(matched_annotation)
            else:
                orphan_images.append(image_path)

        return {
            'matched_pairs': matched_pairs,
            'orphan_images': orphan_images,
            'orphan_annotations': orphan_annotations,
            'total_images': len(image_files),
            'total_annotations': len(annotation_files)
        }

    def _scan_part_directory(self, part_path: str, dataset_source: str) -> Dict[str, Any]:
        """
        扫描单个part目录，构建该目录的文件索引。

        Args:
            part_path (str): part目录的完整路径
            dataset_source (str): 数据源标识

        Returns:
            Dict[str, Any]: 该part目录的扫描结果
                {
                    'file_index': Dict[int, Dict[str, str]],  # 文件索引
                    'statistics': Dict[str, Any]              # 统计信息
                }
        """
        part_name = os.path.basename(part_path)
        file_index = {}

        try:
            # 获取目录中的所有文件
            all_files = []
            for item in os.listdir(part_path):
                item_path = os.path.join(part_path, item)
                if os.path.isfile(item_path):
                    all_files.append(item_path)

            # 匹配图像-标注文件对
            match_result = self._match_image_annotation_pairs(all_files)

            # 为每个匹配对生成文件索引条目
            for image_path, annotation_path in match_result['matched_pairs']:
                # 验证文件对
                if not self._validate_file_pair(image_path, annotation_path):
                    continue

                # 生成image_id
                image_id = self._generate_image_id(image_path)

                # 构建文件索引条目
                file_index[image_id] = {
                    'image_path': self._normalize_path(image_path),
                    'annotation_path': self._normalize_path(annotation_path),
                    'part_dir': part_name,
                    'dataset_source': dataset_source,
                    'relative_path': os.path.join(part_name, os.path.basename(image_path))
                }

            # 构建统计信息
            statistics = {
                'part_name': part_name,
                'part_path': part_path,
                'total_images': match_result['total_images'],
                'total_annotations': match_result['total_annotations'],
                'valid_pairs': len(match_result['matched_pairs']),
                'orphan_images': len(match_result['orphan_images']),
                'orphan_annotations': len(match_result['orphan_annotations'])
            }

            return {
                'file_index': file_index,
                'statistics': statistics
            }

        except (OSError, PermissionError):
            # 遵循fail-fast原则，目录访问错误时返回空结果
            return {
                'file_index': {},
                'statistics': {
                    'part_name': part_name,
                    'part_path': part_path,
                    'total_images': 0,
                    'total_annotations': 0,
                    'valid_pairs': 0,
                    'orphan_images': 0,
                    'orphan_annotations': 0,
                    'error': 'Directory access failed'
                }
            }
