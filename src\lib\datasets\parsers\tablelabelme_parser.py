"""
TableLabelMe格式的具体解析器实现，负责格式转换和数据标准化。

该解析器将TableLabelMe格式的标注数据转换为LORE-TSR兼容的标准格式，
包括坐标转换、逻辑结构转换、面积计算等核心功能。
"""

from __future__ import absolute_import
from __future__ import division
from __future__ import print_function

import math
from typing import Dict, Any, List, Optional, Union

from .base_parser import BaseParser


class TableLabelMeParser(BaseParser):
    """
    TableLabelMe格式解析器，继承BaseParser基类。
    
    负责将TableLabelMe格式的标注数据转换为LORE-TSR标准格式，
    确保与现有训练流程完全兼容。
    """
    
    def parse_file(self, json_path: str, image_path: str) -> Optional[List[Dict[str, Any]]]:
        """
        解析TableLabelMe格式文件，转换为LORE-TSR标准格式。
        
        Args:
            json_path (str): TableLabelMe JSON文件路径
            image_path (str): 对应图像文件路径
            
        Returns:
            Optional[List[Dict[str, Any]]]: 包含所有标注的标准化字典列表，
                                          解析失败时返回None
        """
        # 加载JSON文件
        json_data = self._load_json_file(json_path)
        if json_data is None:
            return None
            
        # 生成图像ID
        image_id = self.generate_image_id(image_path)
        
        # 解析标注数据
        annotations = []
        
        # TableLabelMe格式可能包含多个标注，需要遍历处理
        if isinstance(json_data, list):
            # 如果是标注列表
            raw_annotations = json_data
        elif isinstance(json_data, dict) and 'annotations' in json_data:
            # 如果是包含annotations字段的字典
            raw_annotations = json_data['annotations']
        elif isinstance(json_data, dict):
            # 如果是单个标注对象
            raw_annotations = [json_data]
        else:
            return None
            
        # 质量筛选
        filtered_annotations = self.filter_by_quality(raw_annotations)
        
        # 转换每个标注
        for ann in filtered_annotations:
            try:
                # 检查必需字段
                if not all(key in ann for key in ['bbox', 'lloc', 'cell_ind']):
                    continue
                    
                # 转换坐标
                segmentation = self.convert_bbox_to_segmentation(ann['bbox'])
                if segmentation is None:
                    continue
                    
                # 转换逻辑轴
                logic_axis = self.convert_lloc_to_logic_axis(ann['lloc'])
                if logic_axis is None:
                    continue
                    
                # 计算面积
                area = self.calculate_area(segmentation)
                
                # 提取bbox
                bbox = self.extract_bbox_from_segmentation(segmentation)
                
                # 生成标注ID
                annotation_id = self.generate_annotation_id(image_id, ann['cell_ind'])
                
                # 构建标准化数据结构
                standard_annotation = {
                    'image_id': image_id,
                    'annotation_id': annotation_id,
                    'category_id': 1,  # 固定为1（单元格类别）
                    'segmentation': segmentation,
                    'logic_axis': logic_axis,
                    'area': area,
                    'bbox': bbox,
                    'extra_info': {
                        'table_ind': ann.get('table_ind', 0),
                        'type': ann.get('type', 'cell'),
                        'border': ann.get('border', True),
                        'content': ann.get('content', ''),
                        'quality': ann.get('quality', '合格')
                    }
                }
                
                # 验证数据
                if self.validate_data(standard_annotation):
                    annotations.append(standard_annotation)
                    
            except (KeyError, TypeError, ValueError):
                # 遵循fail-fast原则，单个标注错误不影响其他标注
                continue
                
        return annotations if annotations else None

    def convert_bbox_to_segmentation(self, bbox: Dict[str, Dict[str, Union[int, float]]]) -> Optional[List[float]]:
        """
        将四个角点坐标转换为一维segmentation数组。

        Args:
            bbox (Dict): 包含p1-p4四个点的字典

        Returns:
            Optional[List[float]]: [p1.x, p1.y, p2.x, p2.y, p3.x, p3.y, p4.x, p4.y]
                                  转换失败时返回None
        """
        try:
            # 提取四个角点坐标
            p1 = bbox['p1']
            p2 = bbox['p2']
            p3 = bbox['p3']
            p4 = bbox['p4']

            # 按顺序组合成segmentation数组
            segmentation = [
                float(p1['x']), float(p1['y']),
                float(p2['x']), float(p2['y']),
                float(p3['x']), float(p3['y']),
                float(p4['x']), float(p4['y'])
            ]

            return segmentation

        except (KeyError, TypeError, ValueError):
            return None

    def convert_lloc_to_logic_axis(self, lloc: Dict[str, Union[int, float]]) -> Optional[List[int]]:
        """
        将逻辑位置信息转换为logic_axis格式。

        Args:
            lloc (Dict): 包含行列位置信息的字典

        Returns:
            Optional[List[int]]: [start_row, end_row, start_col, end_col]
                               转换失败时返回None
        """
        try:
            # 直接提取字段值并组合成数组
            logic_axis = [
                int(lloc['start_row']),
                int(lloc['end_row']),
                int(lloc['start_col']),
                int(lloc['end_col'])
            ]

            return logic_axis

        except (KeyError, TypeError, ValueError):
            return None

    def calculate_area(self, segmentation: List[float]) -> float:
        """
        根据segmentation坐标计算单元格面积。

        Args:
            segmentation (List[float]): 8个坐标值的数组

        Returns:
            float: 计算得出的面积

        Note:
            使用鞋带公式计算多边形面积
        """
        if len(segmentation) != 8:
            return 0.0

        try:
            # 提取坐标点
            x = [segmentation[i] for i in range(0, 8, 2)]
            y = [segmentation[i] for i in range(1, 8, 2)]

            # 使用鞋带公式计算面积
            area = 0.0
            n = len(x)
            for i in range(n):
                j = (i + 1) % n
                area += x[i] * y[j]
                area -= x[j] * y[i]

            return abs(area) / 2.0

        except (TypeError, ValueError, IndexError):
            return 0.0

    def extract_bbox_from_segmentation(self, segmentation: List[float]) -> List[float]:
        """
        从segmentation坐标提取bbox格式。

        Args:
            segmentation (List[float]): 8个坐标值的数组

        Returns:
            List[float]: [x, y, width, height]格式的bbox
        """
        if len(segmentation) != 8:
            return [0.0, 0.0, 0.0, 0.0]

        try:
            # 提取所有x和y坐标
            x_coords = [segmentation[i] for i in range(0, 8, 2)]
            y_coords = [segmentation[i] for i in range(1, 8, 2)]

            # 计算边界框
            min_x = min(x_coords)
            max_x = max(x_coords)
            min_y = min(y_coords)
            max_y = max(y_coords)

            # 返回[x, y, width, height]格式
            return [min_x, min_y, max_x - min_x, max_y - min_y]

        except (TypeError, ValueError, IndexError):
            return [0.0, 0.0, 0.0, 0.0]

    def filter_by_quality(self, annotations: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        根据quality字段筛选合格的标注数据。

        Args:
            annotations (List[Dict]): 原始标注列表

        Returns:
            List[Dict]: 筛选后的标注列表
        """
        if not isinstance(annotations, list):
            return []

        filtered = []
        for ann in annotations:
            if isinstance(ann, dict) and ann.get('quality') == '合格':
                filtered.append(ann)

        return filtered
