"""
TableLabelMe格式的数据集类，继承现有Table类的基础功能，重写数据加载逻辑。

该数据集类将TableLabelMe格式的数据转换为与COCO格式兼容的数据结构，
确保与现有训练流程完全兼容。
"""

from __future__ import absolute_import
from __future__ import division
from __future__ import print_function

import numpy as np
import json
import random
import os
from typing import Dict, Any, List, Optional, Union

import torch.utils.data as data

from ..parsers import TableLabelMeParser, FileScanner, QualityFilter
from ...utils.logger_config import LoggerConfig


class Table(data.Dataset):
    """
    TableLabelMe格式数据集类，继承PyTorch Dataset基类。
    
    该类负责加载TableLabelMe格式的数据，并将其转换为与LORE-TSR
    训练流程兼容的标准格式。
    """
    
    # 类属性，与原Table类保持一致
    num_classes = 2
    table_size = 1024
    default_resolution = [1024, 1024]
    mean = np.array([0.40789654, 0.44719302, 0.47026115],
                   dtype=np.float32).reshape(1, 1, 3)
    std = np.array([0.28863828, 0.27408164, 0.27809835],
                  dtype=np.float32).reshape(1, 1, 3)

    def __init__(self, opt, split: str):
        """
        初始化TableLabelMe数据集，设置基本参数和加载数据索引。
        
        Args:
            opt: 配置对象，包含数据路径和训练参数
            split (str): 数据集分割（train/val/test）
        """
        super(Table, self).__init__()
        self.split = split
        self.opt = opt
        
        # 基本参数设置，与原Table类保持一致
        self.max_objs = 300
        self.max_pairs = 900
        self.max_cors = 1200
        
        self.class_name = ['__background__', 'center', 'corner']
        self._valid_ids = [1, 2]
        self.cat_ids = {v: i for i, v in enumerate(self._valid_ids)}
        self.voc_color = [(v // 32 * 64 + 64, (v // 8) % 4 * 64, v % 8 * 32)
                         for v in range(1, self.num_classes + 1)]
        self._data_rng = np.random.RandomState(123)
        self._eig_val = np.array([0.2141788, 0.01817699, 0.00341571],
                                dtype=np.float32)
        self._eig_vec = np.array([
            [-0.58752847, -0.69563484, 0.41340352],
            [-0.5832747, 0.00994535, -0.81221408],
            [-0.56089297, 0.71832671, 0.41158938]
        ], dtype=np.float32)
        
        # 设置日志记录器
        self.logger = LoggerConfig.setup_logger(f"TableLabelMe.{split}")

        # 初始化TableLabelMe解析器
        self.parser = TableLabelMeParser()

        # 初始化文件扫描器
        self.file_scanner = FileScanner()

        # 创建质量筛选器
        quality_config = getattr(opt, 'quality_filter_config', None)
        self.quality_filter = QualityFilter(config=quality_config, logger=self.logger)

        self.logger.info(f'==> initializing TableLabelMe {split} data.')

        # 集成配置系统（迭代4新增功能）
        self._integrate_config_system()

        # 构建文件索引（集成质量筛选）
        self.file_index = self._build_file_index()

        # 加载标注数据
        self._load_annotations()

        self.num_samples = len(self.images)
        self.logger.info(f'TableLabelMe数据集初始化完成 - {split}: {self.num_samples}个样本')

    def _integrate_config_system(self) -> None:
        """
        集成配置系统，支持动态配置和多格式数据加载

        该方法集成步骤4.1-4.3的成果：
        - ConfigLoader: 配置文件加载
        - 配置文件系统: 预定义配置
        - 参数解析系统: 统一参数管理
        """
        try:
            self.logger.info("开始集成配置系统")

            # 检查是否有配置系统集成的参数
            dataset_mode = getattr(self.opt, 'dataset_mode', 'COCO')
            config_data = getattr(self.opt, 'config_data', None)
            unified_config = getattr(self.opt, 'unified_config', None)
            data_paths = getattr(self.opt, 'data_paths', {})

            self.logger.info(f"检测到数据集模式: {dataset_mode}")

            if dataset_mode == 'TableLabelMe' and config_data:
                self.logger.info("检测到TableLabelMe配置，开始集成")

                # 保存配置信息
                self._config_data = config_data
                self._unified_config = unified_config
                self._external_data_paths = data_paths

                # 如果有外部配置的数据路径，使用动态路径切换
                if data_paths and self.split in data_paths:
                    external_paths = data_paths[self.split]
                    if external_paths:
                        self.logger.info(f"使用外部配置的数据路径: {len(external_paths)}个路径")

                        # 动态切换到外部配置的路径
                        success = self.switch_data_paths(external_paths, rebuild_index=False)
                        if success:
                            self.logger.info("外部数据路径集成成功")
                        else:
                            self.logger.warning("外部数据路径集成失败，使用默认路径")

                # 检测和适配数据格式
                detected_format = self.detect_data_format()
                self.logger.info(f"检测到数据格式: {detected_format}")

                # 如果检测到的格式与配置不一致，给出警告
                if detected_format != 'TableLabelMe':
                    self.logger.warning(f"配置为TableLabelMe模式，但检测到{detected_format}格式数据")

            else:
                self.logger.info("使用默认配置（COCO兼容模式）")
                self._config_data = None
                self._unified_config = None
                self._external_data_paths = {}

            self.logger.info("配置系统集成完成")

        except Exception as e:
            self.logger.error(f"配置系统集成失败: {e}")
            # 集成失败时使用默认配置
            self._config_data = None
            self._unified_config = None
            self._external_data_paths = {}

    def _get_data_paths(self) -> List[str]:
        """
        获取数据路径配置，支持外部配置文件（迭代4增强版本）。

        Returns:
            List[str]: 数据路径列表

        Note:
            迭代4支持外部配置文件，同时保持向后兼容性。
            优先级：外部配置 > 硬编码路径
        """
        # 检查是否有外部配置的路径
        external_paths = getattr(self, '_external_data_paths', {})
        if external_paths and self.split in external_paths:
            configured_paths = external_paths[self.split]
            if configured_paths:
                self.logger.debug(f"使用外部配置路径: {len(configured_paths)}个")
                return configured_paths

        # 回退到硬编码路径（保持向后兼容性）
        base_path = "D:/workspace/datasets/cf_train_clean/wired_tables_reorganized/TabRecSet_TableLabelMe_fix/chinese"

        # 对于train和val都使用同一个数据集（迭代2简化版本）
        base_paths = {
            'train': [base_path],
            'val': [base_path]
        }

        default_paths = base_paths.get(self.split, [])
        self.logger.debug(f"使用默认硬编码路径: {len(default_paths)}个")
        return default_paths

    def switch_data_paths(self, new_paths: Union[List[str], Dict[str, List[str]]],
                         rebuild_index: bool = True) -> bool:
        """
        动态切换数据路径，支持运行时路径更新

        Args:
            new_paths: 新的数据路径配置
                - List[str]: 当前split的路径列表
                - Dict[str, List[str]]: 完整的split->paths映射
            rebuild_index: 是否重新构建文件索引

        Returns:
            bool: 切换是否成功
        """
        try:
            self.logger.info(f"开始切换数据路径 - split: {self.split}")

            # 验证新路径格式
            if isinstance(new_paths, list):
                # 单个split的路径列表
                target_paths = new_paths
            elif isinstance(new_paths, dict):
                # 完整的路径映射
                target_paths = new_paths.get(self.split, [])
            else:
                self.logger.error(f"不支持的路径格式: {type(new_paths)}")
                return False

            # 验证路径有效性
            valid_paths = []
            for path in target_paths:
                if not isinstance(path, str):
                    self.logger.warning(f"跳过非字符串路径: {path}")
                    continue

                if not os.path.exists(path):
                    self.logger.warning(f"路径不存在，跳过: {path}")
                    continue

                if not os.access(path, os.R_OK):
                    self.logger.warning(f"路径不可读，跳过: {path}")
                    continue

                valid_paths.append(os.path.abspath(path))

            if not valid_paths:
                self.logger.error("没有有效的数据路径")
                return False

            # 保存原始路径（用于回滚）
            original_get_data_paths = self._get_data_paths

            # 动态更新_get_data_paths方法
            def new_get_data_paths():
                return valid_paths

            self._get_data_paths = new_get_data_paths

            # 重新构建文件索引（如果需要）
            if rebuild_index:
                try:
                    old_file_index = getattr(self, 'file_index', {})
                    old_num_samples = getattr(self, 'num_samples', 0)

                    # 重新构建索引
                    self.file_index = self._build_file_index()

                    # 重新加载标注
                    self._load_annotations()

                    # 更新样本数量
                    self.num_samples = len(self.images)

                    self.logger.info(f"路径切换成功 - 新路径数量: {len(valid_paths)}, "
                                   f"样本数量: {old_num_samples} -> {self.num_samples}")

                except Exception as e:
                    # 回滚到原始状态
                    self.logger.error(f"重建索引失败，回滚: {e}")
                    self._get_data_paths = original_get_data_paths
                    if 'old_file_index' in locals():
                        self.file_index = old_file_index
                        self.num_samples = old_num_samples
                    return False
            else:
                self.logger.info(f"路径切换成功 - 新路径数量: {len(valid_paths)} (未重建索引)")

            return True

        except Exception as e:
            self.logger.error(f"路径切换失败: {e}")
            return False

    def detect_data_format(self, data_paths: Optional[List[str]] = None) -> str:
        """
        自动检测数据格式（COCO或TableLabelMe）

        Args:
            data_paths: 要检测的数据路径列表，如果为None则使用当前配置的路径

        Returns:
            str: 数据格式 ('COCO' 或 'TableLabelMe')
        """
        try:
            # 获取要检测的路径
            if data_paths is None:
                data_paths = self._get_data_paths()

            if not data_paths:
                self.logger.warning("没有可检测的数据路径，默认返回TableLabelMe格式")
                return 'TableLabelMe'

            tableme_indicators = 0
            coco_indicators = 0
            total_paths_checked = 0

            for path in data_paths:
                if not os.path.exists(path):
                    continue

                total_paths_checked += 1
                self.logger.debug(f"检测路径格式: {path}")

                # 检查TableLabelMe格式特征
                tableme_score = self._check_tableme_format(path)

                # 检查COCO格式特征
                coco_score = self._check_coco_format(path)

                if tableme_score > coco_score:
                    tableme_indicators += 1
                elif coco_score > tableme_score:
                    coco_indicators += 1

                self.logger.debug(f"路径 {path} - TableLabelMe得分: {tableme_score}, COCO得分: {coco_score}")

            if total_paths_checked == 0:
                self.logger.warning("没有有效路径可检测，默认返回TableLabelMe格式")
                return 'TableLabelMe'

            # 根据检测结果确定格式
            if tableme_indicators > coco_indicators:
                detected_format = 'TableLabelMe'
            elif coco_indicators > tableme_indicators:
                detected_format = 'COCO'
            else:
                # 平局时默认为TableLabelMe（因为这是TableLabelMe数据集类）
                detected_format = 'TableLabelMe'

            self.logger.info(f"数据格式检测完成: {detected_format} "
                           f"(TableLabelMe: {tableme_indicators}, COCO: {coco_indicators}, "
                           f"检测路径数: {total_paths_checked})")

            return detected_format

        except Exception as e:
            self.logger.error(f"数据格式检测失败: {e}")
            return 'TableLabelMe'  # 默认格式

    def _check_tableme_format(self, path: str) -> int:
        """
        检查路径是否符合TableLabelMe格式特征

        Args:
            path: 要检测的路径

        Returns:
            int: TableLabelMe格式得分（0-10）
        """
        score = 0

        try:
            # 检查是否有part_xxxx目录结构
            if os.path.isdir(path):
                subdirs = [d for d in os.listdir(path)
                          if os.path.isdir(os.path.join(path, d)) and d.startswith('part_')]
                if subdirs:
                    score += 5  # part目录结构是强特征

                    # 检查part目录内部结构
                    sample_part = os.path.join(path, subdirs[0])
                    part_files = os.listdir(sample_part)

                    # 检查是否有.json文件
                    json_files = [f for f in part_files if f.endswith('.json')]
                    if json_files:
                        score += 2

                        # 检查JSON文件内容格式
                        sample_json = os.path.join(sample_part, json_files[0])
                        try:
                            with open(sample_json, 'r', encoding='utf-8') as f:
                                data = json.load(f)
                                # TableLabelMe特征：shapes字段
                                if 'shapes' in data:
                                    score += 2
                                # TableLabelMe特征：imageData字段
                                if 'imageData' in data:
                                    score += 1
                        except:
                            pass

            # 检查路径名称特征
            if 'TableLabelMe' in path or 'tablelabelme' in path.lower():
                score += 1

        except Exception as e:
            self.logger.debug(f"TableLabelMe格式检测异常: {e}")

        return min(score, 10)

    def _check_coco_format(self, path: str) -> int:
        """
        检查路径是否符合COCO格式特征

        Args:
            path: 要检测的路径

        Returns:
            int: COCO格式得分（0-10）
        """
        score = 0

        try:
            if os.path.isdir(path):
                files = os.listdir(path)

                # 检查是否有annotations.json或类似文件
                annotation_files = [f for f in files if 'annotation' in f.lower() and f.endswith('.json')]
                if annotation_files:
                    score += 5

                    # 检查COCO JSON格式
                    sample_annotation = os.path.join(path, annotation_files[0])
                    try:
                        with open(sample_annotation, 'r', encoding='utf-8') as f:
                            data = json.load(f)
                            # COCO特征：images, annotations, categories字段
                            if all(key in data for key in ['images', 'annotations', 'categories']):
                                score += 3
                            elif any(key in data for key in ['images', 'annotations']):
                                score += 2
                    except:
                        pass

                # 检查是否有images目录
                if 'images' in files and os.path.isdir(os.path.join(path, 'images')):
                    score += 2

            # 检查路径名称特征
            if 'coco' in path.lower() or 'wtw' in path.lower():
                score += 1

        except Exception as e:
            self.logger.debug(f"COCO格式检测异常: {e}")

        return min(score, 10)

    def load_data_with_format(self, data_format: Optional[str] = None,
                             force_reload: bool = False) -> bool:
        """
        根据数据格式适配数据加载逻辑

        Args:
            data_format: 指定的数据格式，如果为None则自动检测
            force_reload: 是否强制重新加载数据

        Returns:
            bool: 加载是否成功
        """
        try:
            # 自动检测数据格式（如果未指定）
            if data_format is None:
                data_format = self.detect_data_format()

            self.logger.info(f"使用数据格式: {data_format}")

            # 检查是否需要重新加载
            current_format = getattr(self, '_current_data_format', None)
            if not force_reload and current_format == data_format:
                self.logger.info(f"数据格式未变化({data_format})，跳过重新加载")
                return True

            # 保存当前状态（用于回滚）
            backup_state = self._backup_current_state()

            try:
                if data_format == 'TableLabelMe':
                    success = self._load_tableme_format()
                elif data_format == 'COCO':
                    success = self._load_coco_format()
                else:
                    self.logger.error(f"不支持的数据格式: {data_format}")
                    return False

                if success:
                    self._current_data_format = data_format
                    self.logger.info(f"数据格式 {data_format} 加载成功")
                    return True
                else:
                    # 加载失败，回滚状态
                    self._restore_state(backup_state)
                    self.logger.error(f"数据格式 {data_format} 加载失败，已回滚")
                    return False

            except Exception as e:
                # 异常时回滚状态
                self._restore_state(backup_state)
                self.logger.error(f"数据加载异常，已回滚: {e}")
                return False

        except Exception as e:
            self.logger.error(f"数据格式适配失败: {e}")
            return False

    def _load_tableme_format(self) -> bool:
        """
        加载TableLabelMe格式数据

        Returns:
            bool: 加载是否成功
        """
        try:
            self.logger.info("开始加载TableLabelMe格式数据")

            # 使用现有的TableLabelMe加载逻辑
            self.file_index = self._build_file_index()
            self._load_annotations()
            self.num_samples = len(self.images)

            self.logger.info(f"TableLabelMe格式数据加载完成，样本数: {self.num_samples}")
            return True

        except Exception as e:
            self.logger.error(f"TableLabelMe格式数据加载失败: {e}")
            return False

    def _load_coco_format(self) -> bool:
        """
        加载COCO格式数据（兼容性支持）

        Returns:
            bool: 加载是否成功
        """
        try:
            self.logger.info("开始加载COCO格式数据")

            # COCO格式的简化加载逻辑
            # 注意：这里只是为了兼容性，实际的COCO数据应该使用专门的COCO数据集类
            data_paths = self._get_data_paths()

            if not data_paths:
                self.logger.error("没有可用的COCO数据路径")
                return False

            # 简化的COCO数据加载
            self.images = []
            self.anns = {}

            for path in data_paths:
                if not os.path.exists(path):
                    continue

                # 查找COCO标注文件
                annotation_files = []
                if os.path.isdir(path):
                    files = os.listdir(path)
                    annotation_files = [f for f in files if 'annotation' in f.lower() and f.endswith('.json')]

                if annotation_files:
                    annotation_file = os.path.join(path, annotation_files[0])
                    try:
                        with open(annotation_file, 'r', encoding='utf-8') as f:
                            coco_data = json.load(f)

                        # 简化处理COCO数据
                        if 'images' in coco_data:
                            self.images.extend(coco_data['images'])

                        if 'annotations' in coco_data:
                            for ann in coco_data['annotations']:
                                img_id = ann.get('image_id')
                                if img_id not in self.anns:
                                    self.anns[img_id] = []
                                self.anns[img_id].append(ann)

                    except Exception as e:
                        self.logger.warning(f"COCO文件解析失败: {annotation_file}, 错误: {e}")

            self.num_samples = len(self.images)
            self.logger.info(f"COCO格式数据加载完成，样本数: {self.num_samples}")

            if self.num_samples == 0:
                self.logger.warning("COCO格式数据加载完成，但没有找到有效样本")
                return False

            return True

        except Exception as e:
            self.logger.error(f"COCO格式数据加载失败: {e}")
            return False

    def _backup_current_state(self) -> Dict[str, Any]:
        """
        备份当前数据状态

        Returns:
            Dict[str, Any]: 当前状态的备份
        """
        return {
            'file_index': getattr(self, 'file_index', {}),
            'images': getattr(self, 'images', []),
            'anns': getattr(self, 'anns', {}),
            'num_samples': getattr(self, 'num_samples', 0),
            'current_data_format': getattr(self, '_current_data_format', None)
        }

    def _restore_state(self, backup_state: Dict[str, Any]) -> None:
        """
        恢复数据状态

        Args:
            backup_state: 要恢复的状态备份
        """
        self.file_index = backup_state.get('file_index', {})
        self.images = backup_state.get('images', [])
        self.anns = backup_state.get('anns', {})
        self.num_samples = backup_state.get('num_samples', 0)
        self._current_data_format = backup_state.get('current_data_format', None)

    def _build_file_index(self) -> Dict[int, Dict[str, str]]:
        """
        构建文件索引并进行质量筛选。

        Returns:
            Dict[int, Dict[str, str]]: 经过质量筛选的文件映射字典

        Note:
            集成质量筛选功能，在文件扫描后进行质量筛选，
            确保只加载符合质量要求的数据样本。
        """
        try:
            # 获取数据路径配置
            data_paths = self._get_data_paths()

            if not data_paths:
                self.logger.warning(f'未找到{self.split}分割的数据路径配置')
                return {}

            # 使用FileScanner进行目录扫描
            scan_result = self.file_scanner.scan_directories(data_paths, self.split)

            # 保存扫描统计信息
            self.scan_statistics = scan_result.get('statistics', {})

            # 获取原始文件索引
            raw_file_index = scan_result.get('file_index', {})

            self.logger.info(f'原始文件扫描完成，发现 {len(raw_file_index)} 个文件对')
            if hasattr(self, 'scan_statistics'):
                stats = self.scan_statistics
                self.logger.info(f'扫描统计：{stats.get("valid_pairs", 0)}个有效对，'
                              f'{stats.get("orphan_images", 0)}个孤儿图像，'
                              f'扫描时间：{stats.get("scan_time", 0):.3f}秒')

            # 进行质量筛选
            filter_result = self.quality_filter.filter_samples(raw_file_index, self.split)

            # 保存筛选结果和统计信息
            self.filter_statistics = filter_result["statistics"]
            self.exception_report = filter_result["exception_report"]

            # 记录筛选结果
            stats = self.filter_statistics
            self.logger.info(f'质量筛选完成 - 有效: {stats["valid_samples"]}, '
                            f'筛选掉: {stats["filtered_samples"]}, '
                            f'错误: {stats["error_samples"]}, '
                            f'成功率: {self.exception_report["summary"]["success_rate"]:.1f}%')

            return filter_result["filtered_index"]

        except Exception as e:
            self.logger.error(f'文件索引构建失败：{e}')
            self.logger.info('回退到空索引')
            # 保存错误信息到统计中
            self.scan_statistics = {
                'error': str(e),
                'valid_pairs': 0,
                'total_images': 0,
                'total_annotations': 0
            }
            self.filter_statistics = {
                'total_processed': 0,
                'valid_samples': 0,
                'filtered_samples': 0,
                'error_samples': 0
            }
            self.exception_report = {
                'summary': {'success_rate': 0.0}
            }
            return {}

    def get_quality_report(self) -> Dict[str, Any]:
        """
        获取质量筛选报告。

        Returns:
            Dict[str, Any]: 包含统计信息和异常报告的字典
        """
        return {
            "statistics": getattr(self, 'filter_statistics', {}),
            "exception_report": getattr(self, 'exception_report', {}),
            "scan_statistics": getattr(self, 'scan_statistics', {})
        }

    def _load_annotations(self):
        """
        加载并解析所有TableLabelMe标注文件。
        
        Note:
            将TableLabelMe格式转换为与COCO兼容的数据结构，
            确保与现有训练流程完全兼容。
        """
        self.images = []
        self.annotations = {}
        self.image_info = {}
        
        for image_id, file_info in self.file_index.items():
            # MVP版本：跳过实际文件解析，使用模拟数据
            # 在实际使用时，这里会调用解析器处理真实文件
            
            # 模拟图像信息
            self.image_info[image_id] = {
                'id': image_id,
                'file_name': file_info['image_path'],
                'width': 1024,
                'height': 768
            }
            
            # 模拟标注数据（与COCO格式兼容）
            mock_annotations = self._create_mock_annotations(image_id)
            self.annotations[image_id] = mock_annotations
            
            self.images.append(image_id)
    
    def _create_mock_annotations(self, image_id: int) -> List[Dict[str, Any]]:
        """
        创建模拟标注数据，用于MVP版本测试。
        
        Args:
            image_id (int): 图像ID
            
        Returns:
            List[Dict[str, Any]]: 模拟的标注数据列表
            
        Note:
            返回的数据结构与COCO格式完全兼容，确保与现有训练流程无缝集成。
        """
        # 创建一个示例标注，格式与COCO兼容
        mock_annotation = {
            'id': image_id * 1000 + 1,
            'image_id': image_id,
            'category_id': 1,
            'segmentation': [[100.0, 50.0, 200.0, 50.0, 200.0, 100.0, 100.0, 100.0]],
            'logic_axis': [[0, 0, 0, 1]],
            'area': 5000.0,
            'bbox': [100.0, 50.0, 100.0, 50.0],
            'iscrowd': 0,
            'ignore': 0
        }
        
        return [mock_annotation]

    def __len__(self) -> int:
        """
        返回数据集大小。

        Returns:
            int: 数据集中的样本数量
        """
        return self.num_samples

    def _get_image_info(self, index: int) -> Dict[str, Any]:
        """
        获取指定索引的图像信息。

        Args:
            index (int): 图像索引

        Returns:
            Dict[str, Any]: 图像信息字典
        """
        img_id = self.images[index]
        return self.image_info[img_id]

    def getImgIds(self) -> List[int]:
        """
        获取所有图像ID列表，兼容COCO API接口。

        Returns:
            List[int]: 图像ID列表
        """
        return self.images

    def loadImgs(self, ids: List[int]) -> List[Dict[str, Any]]:
        """
        加载指定ID的图像信息，兼容COCO API接口。

        Args:
            ids (List[int]): 图像ID列表

        Returns:
            List[Dict[str, Any]]: 图像信息列表
        """
        return [self.image_info[img_id] for img_id in ids if img_id in self.image_info]

    def getAnnIds(self, imgIds: List[int]) -> List[int]:
        """
        获取指定图像的标注ID列表，兼容COCO API接口。

        Args:
            imgIds (List[int]): 图像ID列表

        Returns:
            List[int]: 标注ID列表
        """
        ann_ids = []
        for img_id in imgIds:
            if img_id in self.annotations:
                for ann in self.annotations[img_id]:
                    ann_ids.append(ann['id'])
        return ann_ids

    def loadAnns(self, ids: List[int]) -> List[Dict[str, Any]]:
        """
        加载指定ID的标注信息，兼容COCO API接口。

        Args:
            ids (List[int]): 标注ID列表

        Returns:
            List[Dict[str, Any]]: 标注信息列表
        """
        annotations = []
        for img_id in self.annotations:
            for ann in self.annotations[img_id]:
                if ann['id'] in ids:
                    annotations.append(ann)
        return annotations

    def _to_float(self, x: Union[int, float]) -> float:
        """
        数值转换为浮点数，保留两位小数。

        Args:
            x (Union[int, float]): 输入数值

        Returns:
            float: 格式化后的浮点数
        """
        return float("{:.2f}".format(x))

    def convert_eval_format(self, all_bboxes: Dict, thresh: float) -> List[Dict[str, Any]]:
        """
        转换评估格式，兼容原Table类接口。

        Args:
            all_bboxes (Dict): 所有边界框预测结果
            thresh (float): 置信度阈值

        Returns:
            List[Dict[str, Any]]: 转换后的检测结果
        """
        detections = []
        for image_id in all_bboxes:
            for cls_ind in all_bboxes[image_id]:
                category_id = self._valid_ids[cls_ind - 1]
                for bbox in all_bboxes[image_id][cls_ind]:
                    if bbox[4] > float(thresh):
                        bbox[2] -= bbox[0]
                        bbox[3] -= bbox[1]
                        score = bbox[4]
                        bbox_out = list(map(self._to_float, bbox[0:4]))

                        detection = {
                            "image_id": int(image_id),
                            "category_id": int(category_id),
                            "bbox": bbox_out,
                            "score": float("{:.2f}".format(score))
                        }
                        if len(bbox) > 5:
                            extreme_points = list(map(self._to_float, bbox[5:13]))
                            detection["extreme_points"] = extreme_points
                        detections.append(detection)
        print('total:', len(detections))
        return detections

    def save_results(self, results: Dict, save_dir: str, thresh: float):
        """
        保存评估结果，兼容原Table类接口。

        Args:
            results (Dict): 预测结果
            save_dir (str): 保存目录
            thresh (float): 置信度阈值
        """
        json.dump(self.convert_eval_format(results, thresh),
                 open('{}/results.json'.format(save_dir), 'w'))

    def run_eval(self, results: Dict, save_dir: str, thresh: float):
        """
        运行评估，兼容原Table类接口。

        Args:
            results (Dict): 预测结果
            save_dir (str): 保存目录
            thresh (float): 置信度阈值

        Note:
            MVP版本暂不实现完整的COCO评估，仅保存结果。
            完整的评估功能将在后续迭代中实现。
        """
        self.save_results(results, save_dir, thresh)
        print('TableLabelMe评估结果已保存到: {}/results.json'.format(save_dir))
        print('注意：MVP版本暂不支持完整的COCO评估指标计算')
