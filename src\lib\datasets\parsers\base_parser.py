"""
解析器基类，为后续支持其他数据格式提供统一接口。

该基类定义了数据格式解析器的标准接口，包括文件解析、数据验证、
ID生成等通用功能。所有具体的格式解析器都应继承此基类。
"""

from __future__ import absolute_import
from __future__ import division
from __future__ import print_function

import json
import hashlib
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, Union


class BaseParser(ABC):
    """
    解析器基类，为后续支持其他数据格式提供统一接口。
    
    该基类定义了数据格式解析器的标准接口，确保所有解析器
    都能生成与LORE-TSR兼容的标准化数据结构。
    """
    
    @abstractmethod
    def parse_file(self, json_path: str, image_path: str) -> Optional[Dict[str, Any]]:
        """
        解析单个标注文件，返回标准化数据字典。
        
        Args:
            json_path (str): 标注文件路径
            image_path (str): 对应图像文件路径
            
        Returns:
            Optional[Dict[str, Any]]: 标准化的数据字典，解析失败时返回None
            
        Note:
            子类必须实现此方法，返回的数据结构应与LORE-TSR的
            Dataset.__getitem__返回格式兼容。
        """
        pass
    
    def validate_data(self, data: Dict[str, Any]) -> bool:
        """
        验证解析后数据的完整性和正确性。
        
        Args:
            data (Dict[str, Any]): 解析后的数据字典
            
        Returns:
            bool: 验证结果，True表示数据有效
            
        Note:
            检查必需字段存在性和数据类型正确性，确保数据
            能够被LORE-TSR训练流程正确处理。
        """
        if not isinstance(data, dict):
            return False
            
        # 检查必需字段
        required_fields = ['image_id', 'annotation_id', 'category_id', 
                          'segmentation', 'logic_axis', 'area', 'bbox']
        
        for field in required_fields:
            if field not in data:
                return False
                
        # 检查数据类型
        if not isinstance(data['image_id'], int):
            return False
        if not isinstance(data['annotation_id'], int):
            return False
        if not isinstance(data['category_id'], int):
            return False
        if not isinstance(data['segmentation'], list):
            return False
        if not isinstance(data['logic_axis'], list):
            return False
        if not isinstance(data['area'], (int, float)):
            return False
        if not isinstance(data['bbox'], list):
            return False
            
        return True
    
    def generate_image_id(self, file_path: str) -> int:
        """
        基于文件路径生成稳定的图像ID。
        
        Args:
            file_path (str): 文件路径字符串
            
        Returns:
            int: 唯一的图像ID
            
        Note:
            使用MD5哈希算法确保ID的稳定性和唯一性，
            相同路径总是生成相同的ID。
        """
        # 使用MD5哈希生成稳定的ID
        hash_object = hashlib.md5(file_path.encode('utf-8'))
        # 取前8位十六进制字符转换为整数
        return int(hash_object.hexdigest()[:8], 16)
    
    def generate_annotation_id(self, image_id: int, cell_ind: int) -> int:
        """
        生成全局唯一的标注ID。
        
        Args:
            image_id (int): 图像ID
            cell_ind (int): 单元格索引
            
        Returns:
            int: 唯一的标注ID
            
        Note:
            组合image_id和cell_ind生成唯一标识，确保
            在整个数据集中标注ID的唯一性。
        """
        # 将image_id左移16位，然后加上cell_ind
        # 这样可以确保在同一图像中不同单元格有不同的ID
        return (image_id << 16) + cell_ind
    
    def _load_json_file(self, json_path: str) -> Optional[Dict[str, Any]]:
        """
        安全地加载JSON文件。
        
        Args:
            json_path (str): JSON文件路径
            
        Returns:
            Optional[Dict[str, Any]]: JSON内容字典，加载失败时返回None
            
        Note:
            遵循fail-fast原则，JSON格式错误时返回None而非抛出异常。
        """
        try:
            with open(json_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (FileNotFoundError, json.JSONDecodeError, UnicodeDecodeError):
            # 遵循fail-fast原则，让错误尽早暴露
            return None
